import { execSync } from 'node:child_process'
import { existsSync } from 'node:fs'
import { resolve } from 'node:path'
import process from 'node:process'
import { config } from 'dotenv'

// 确定当前环境
const NODE_ENV = process.env.NODE_ENV || 'development'
const envFile = NODE_ENV === 'production' ? '.env.production' : '.env.development'
const envPath = resolve(process.cwd(), envFile)

// 加载环境变量
if (existsSync(envPath)) {
  config({ path: envPath })
}
else {
  console.warn(`环境变量文件 ${envFile} 不存在，将使用默认值`)
}

const moduleNames = [
  'gaia-product',
  // 'gaia-workflow',
  // 'gaia-user',
  // 'gaia-fulfillment',
  // 'gaia-supplier',
  // 'gaia-file',
]

const moduleToDirectoryMap = {
  'gaia-product': 'product',
  'gaia-workflow': 'workflow',
  'gaia-user': 'user',
  'gaia-fulfillment': 'fulfillment',
  'gaia-supplier': 'supplier',
  'gaia-file': 'file',
}

// 从环境变量中获取 API 网关地址，如果不存在则使用默认值
const root = process.env.VITE_API_BASE_URL || 'https://gateway-test.caguuu.cn'
const determinedModuleName = process.argv[2]

const moduleNamesToGenerate = determinedModuleName ? [determinedModuleName] : moduleNames

const JAVA_OPTS = '-Dio.swagger.parser.util.RemoteUrl.trustAll=true -Dio.swagger.v3.parser.util.RemoteUrl.trustAll=true'

moduleNamesToGenerate.forEach((moduleName) => {
  const outputDir = moduleToDirectoryMap[moduleName] || moduleName.replace('gaia-', '')

  const generateApi = () => {
    const command = `JAVA_OPTS="${JAVA_OPTS}" openapi-generator-cli generate \
    -i ${root}/${moduleName}/v3/api-docs \
    -g typescript-axios \
    -o ./src/apiv2/${outputDir} \
    --skip-validate-spec \
    --additional-properties=enumPropertyNaming=UPPERCASE,modelPropertyNaming=original,supportsES6=true,withSeparateModelsAndApi=true,modelPackage=models,apiPackage=services,useSingleRequestParameter=true,allowUnicodeIdentifiers=true`

    try {
      execSync(command, { stdio: 'inherit' })
      // eslint-disable-next-line no-console
      console.log(`成功在 ./src/apiv2/${outputDir} 生成 ${moduleName} 的 API`)
    }
    catch (error) {
      console.error(`生成 ${moduleName} 的 API 时出错:`, error)
    }
  }

  generateApi()
})
