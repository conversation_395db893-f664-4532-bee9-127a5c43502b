import type { DictValueVO, ProductSkuLogisticsDTO, ProductSkuLogisticsVO, ProductSkuPackageDTO, ProductSkuPackageVO, ProductSkuSpecDTO, ProductSkuUpdateRequest, ProductSkuVO, PropertyValueVO, SkuSpecVO } from '@/apiv2/product'

/**
 * 将单个 ProductSkuVO 对象转换为 ProductSkuUpdateRequest 对象。
 * @param skuVO - 产品 SKU 视图对象 (来自 getProduct 接口)
 * @returns 产品 SKU 更新请求对象 (用于 updateProduct 接口)
 */
function convertProductSkuVOToUpdateRequest(skuVO: ProductSkuVO): ProductSkuUpdateRequest {
  if (!skuVO) {
    // 或者可以抛出错误，取决于调用方的期望
    throw new Error('Input skuVO cannot be null or undefined.')
  }

  // 辅助函数：转换 SkuSpecVO[] 到 ProductSkuSpecDTO[]
  const convertSpecs = (specs: SkuSpecVO[] | undefined): ProductSkuSpecDTO[] | undefined => {
    return specs?.map(spec => ({
      id: spec.id, // 保留 id 用于更新
      length: spec.length ? spec.length / 10 : 0,
      width: spec.width ? spec.width / 10 : 0,
      height: spec.height ? spec.height / 10 : 0,
    }))
  }

  // 辅助函数：转换 ProductSkuPackageVO[] 到 ProductSkuPackageDTO[]
  const convertPackages = (packages: ProductSkuPackageVO[] | undefined): ProductSkuPackageDTO[] | undefined => {
    return packages?.map(pkg => ({
      id: pkg.id, // 保留 id 用于更新
      length: pkg.length ? pkg.length / 10 : 0,
      width: pkg.width ? pkg.width / 10 : 0,
      height: pkg.height ? pkg.height / 10 : 0,
    } as ProductSkuPackageDTO)) // 明确类型，因为 DTO 要求 length/width/height 非可选
  }

  // 辅助函数：转换 ProductSkuLogisticsVO[] 到 ProductSkuLogisticsDTO[]
  const convertLogistics = (logistics: ProductSkuLogisticsVO[] | undefined): ProductSkuLogisticsDTO[] | undefined => {
    return logistics?.map(log => ({
      id: log.id, // 保留 id 用于更新
      locale: log.locale,
      transportMethod: log.transportMethod,
      logisticsChannel: log.logisticsChannel,
      oceanFreightEstimate: log.oceanFreightEstimate,
      lastMileFreightEstimate: log.lastMileFreightEstimate,
      estimatedDeliveryDays: log.estimatedDeliveryDays,
    }))
  }

  // 辅助函数：安全地提取 DictValueVO 的 ID
  const getDictId = (dict: DictValueVO | undefined): number | undefined => dict?.id

  // 辅助函数：安全地提取 PropertyValueVO 的 ID 列表
  const getPropertyIds = (properties: PropertyValueVO[] | undefined): number[] | undefined => {
    return properties?.map(p => p.id as number).filter(id => id !== undefined)
  }

  // 辅助函数：安全地提取 DictValueVO 的 ID 列表
  const getDictIds = (dicts: DictValueVO[] | undefined): number[] | undefined => {
    return dicts?.map(d => d.id as number).filter(id => id !== undefined)
  }

  // --- 执行转换 ---
  const skuUpdateRequest: ProductSkuUpdateRequest = {
    // 必填字段 (确保 skuVO 中有值，否则可能需要错误处理或默认值)
    skuCode: skuVO.skuCode ?? '', // skuCode 在 ProductSkuVO 中是可选的，但在 Request 中是必填
    skuStatus: skuVO.status!, // status 在 ProductSkuVO 中是可选的，但在 Request 中是必填。需要确保 skuVO.status 有效
    netWeight: skuVO.netWeight ? skuVO.netWeight / 1000 : 0, // netWeight 在 ProductSkuVO 中可选，Request 中必填
    grossWeight: skuVO.grossWeight ? skuVO.grossWeight / 1000 : 0,
    purchasePrice: skuVO.purchasePrice ?? 0, // purchasePrice 在 ProductSkuVO 中可选，Request 中必填
    purchasePriceExFreight: skuVO.purchasePriceExFreight ?? 0, // purchasePriceExFreight 在 ProductSkuVO 中可选，Request 中必填
    taxRate: skuVO.taxRate ?? 0, // taxRate 在 ProductSkuVO 中可选，Request 中必填
    platformPrice: skuVO.platformPrice ?? 0, // platformPrice 在 ProductSkuVO 中可选，Request 中必填
    priMatLv1: getDictId(skuVO.priMatLv1)!, // priMatLv1 在 ProductSkuVO 中可选，Request 中必填

    // 可选字段
    skuId: skuVO.id, // 用于标识要更新的 SKU
    skuName: skuVO.skuName,
    skuNameEn: skuVO.skuNameEn,
    thumbnail: skuVO.thumbnail,
    properties: getPropertyIds(skuVO.properties),
    color: getDictId(skuVO.color),
    purchaseRemark: skuVO.purchaseRemark,
    description: skuVO.description,
    appendant: skuVO.appendant,
    installDifficulty: getDictId(skuVO.installDifficulty),
    electricAccessory: getDictIds(skuVO.electronicAccessory),
    electronicState: skuVO.electronicState,
    electronicCertification: getDictId(skuVO.electronicCertification),
    battery: skuVO.battery?.id,
    fragile: skuVO.fragile?.id,
    specs: convertSpecs(skuVO.specs),
    packages: convertPackages(skuVO.packages),
    logistics: convertLogistics(skuVO.logistics),
    priMatLv2: getDictId(skuVO.priMatLv2),
    secMatLv1: getDictId(skuVO.secMatLv1),
    secMatLv2: getDictId(skuVO.secMatLv2),
    materialDesc: skuVO.materialDesc,
    spec1: skuVO.spec1,
    spec2: skuVO.spec2,
    spec3: skuVO.spec3,
    spec4: skuVO.spec4,
    spec5: skuVO.spec5,
    installDocCollectionState: getDictId(skuVO.installDocCollectionState),
    onlinePurchaseLink: skuVO.onlinePurchaseLink,
    supplierDeliveryCode: skuVO.supplierDeliveryCode,
  }

  // 清理掉值为 undefined 的可选字段（如果后端不接受 null 或 undefined）
  // (根据后端实际情况决定是否需要此步骤)
  // Object.keys(skuUpdateRequest).forEach(key => {
  //   if (skuUpdateRequest[key as keyof ProductSkuUpdateRequest] === undefined) {
  //     delete skuUpdateRequest[key as keyof ProductSkuUpdateRequest];
  //   }
  // });

  return skuUpdateRequest
}

/**
 * 将 ProductSkuVO 数组转换为 ProductSkuUpdateRequest 数组。
 * @param skuVOList - 产品 SKU 视图对象数组
 * @returns 产品 SKU 更新请求对象数组
 */
function convertProductSkuVOListToUpdateRequestList(skuVOList: ProductSkuVO[] | undefined): ProductSkuUpdateRequest[] {
  if (!skuVOList) {
    return []
  }
  return skuVOList.map(skuVO => convertProductSkuVOToUpdateRequest(skuVO))
}

/*
   * 将API返回的CommoditySkuVO[]转换为组件使用的CommoditySkuUpdateRequest[]
   */
function convertSkuVOToUpdateRequest(skuVOList: any[] | undefined): any[] {
  if (!skuVOList || !skuVOList.length)
    return []

  return skuVOList.map((skuVO) => {
    // 创建一个符合CommoditySkuUpdateRequest类型的对象
    const skuUpdateRequest: any = {
      // 基本信息
      skuId: skuVO.id,
      skuCode: skuVO.skuCode,

      // 产品属性 - 确保始终初始化为数组
      properties: Array.isArray(skuVO.properties as PropertyValueVO[])
        ? skuVO.properties.map((prop: PropertyValueVO) => prop.id)
        : [],
      color: skuVO.color,
      status: skuVO.status,

      // 价格信息
      supplyPrice: skuVO.supplyPrice,
      marketPrice: skuVO.marketPrice,
      promoPrice: skuVO.promoPrice,
      discountRate: skuVO.discountRate,
      coefficient: skuVO.coefficient,
      prices: skuVO.prices,

      // 材质信息
      priMatLv1: skuVO.priMatLv1,
      priMatLv2: skuVO.priMatLv2,
      secMatLv1: skuVO.secMatLv1,
      secMatLv2: skuVO.secMatLv2,

      // 其他字段
      installDifficulty: skuVO.installDifficulty,
      installationFee: skuVO.installationFee,
    }

    return skuUpdateRequest
  })
}

/**
 * 将编辑界面的 SKU 数据转换为 API 需要的格式，执行单位反向转换
 * @param skuData - 编辑界面的 SKU 数据
 * @returns 转换后适合 API 的 SKU 数据
 */
function convertEditedSkuToApiFormat(skuData: any): any {
  if (!skuData) {
    return skuData
  }

  const convertedSku = { ...skuData }

  // 1. 重量单位转换: kg -> g (乘以 1000)
  if (convertedSku.netWeight !== undefined && convertedSku.netWeight !== null) {
    convertedSku.netWeight = convertedSku.netWeight * 1000
  }
  if (convertedSku.grossWeight !== undefined && convertedSku.grossWeight !== null) {
    convertedSku.grossWeight = convertedSku.grossWeight * 1000
  }

  // 2. 尺寸单位转换: cm -> mm (乘以 10)
  if (Array.isArray(convertedSku.specs)) {
    convertedSku.specs = convertedSku.specs.map((spec: any) => ({
      ...spec,
      length: spec.length !== undefined && spec.length !== null ? spec.length * 10 : spec.length,
      width: spec.width !== undefined && spec.width !== null ? spec.width * 10 : spec.width,
      height: spec.height !== undefined && spec.height !== null ? spec.height * 10 : spec.height,
    }))
  }

  if (Array.isArray(convertedSku.packages)) {
    convertedSku.packages = convertedSku.packages.map((pkg: any) => ({
      ...pkg,
      length: pkg.length !== undefined && pkg.length !== null ? pkg.length * 10 : pkg.length,
      width: pkg.width !== undefined && pkg.width !== null ? pkg.width * 10 : pkg.width,
      height: pkg.height !== undefined && pkg.height !== null ? pkg.height * 10 : pkg.height,
    }))
  }

  return convertedSku
}

/**
 * 将编辑界面的 SKU 数据数组转换为 API 需要的格式，执行单位反向转换
 * @param skuDataList - 编辑界面的 SKU 数据数组
 * @returns 转换后适合 API 的 SKU 数据数组
 */
function convertEditedSkuListToApiFormat(skuDataList: any[]): any[] {
  if (!skuDataList || !skuDataList.length) {
    return []
  }
  return skuDataList.map(skuData => convertEditedSkuToApiFormat(skuData))
}

export default {
  convertProductSkuVOToUpdateRequest,
  convertProductSkuVOListToUpdateRequestList,
  convertSkuVOToUpdateRequest,
  convertEditedSkuToApiFormat,
  convertEditedSkuListToApiFormat,
}
